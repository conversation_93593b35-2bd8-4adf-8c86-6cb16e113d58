import { Schema } from "effect";
import {
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Create<PERSON>erson<PERSON><PERSON><PERSON>rom<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	Person<PERSON>rom<PERSON>pi,
	UpdatePersonApi,
	UpdatePersonApiFromUpdatePerson,
} from "~/person/service/repo/api/dto";
import { Client, CreateClient, UpdateClient } from "../../model/client";
import {
	ClientLinkResponse,
	CreatePublicClientLink,
	PublicClientLink,
	SessionInfo,
	TurnInfo,
	UpdatePublicClientLink,
	WorkerInfo,
} from "../../model/publicClientLink";

export const ClientApi = Schema.Struct({
	id: Schema.String,
	person: PersonApi,
	public_link: Schema.NullOr(Schema.String),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const ClientFromApi = Schema.transform(<PERSON><PERSON><PERSON><PERSON>, Client, {
	strict: true,
	decode: (clientApi) => ({
		...clientApi,
		person: Schema.decodeUnknownSync(PersonFromApi)(clientApi.person),
		publicLink: clientApi.public_link,
		createdAt: clientApi.created_at,
		updatedAt: clientApi.updated_at,
		deletedAt: clientApi.deleted_at,
	}),
	encode: (client) => ({
		...client,
		person: Schema.encodeUnknownSync(PersonFromApi)(client.person),
		public_link: client.publicLink,
		created_at: client.createdAt,
		updated_at: client.updatedAt,
		deleted_at: client.deletedAt,
	}),
});

export const ClientListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(ClientFromApi))),
	Schema.mutable(Schema.Array(Client)),
	{
		strict: true,
		decode: (clientApiList) => (clientApiList ? clientApiList : []),
		encode: (clientList) => clientList,
	},
);

export const CreateClientApi = Schema.Struct({
	person: CreatePersonApi,
});

export const CreateClientApiFromCreateClient = Schema.transform(
	CreateClient,
	CreateClientApi,
	{
		strict: true,
		encode: (clientApi) => ({
			...clientApi,
			person: Schema.encodeUnknownSync(CreatePersonApiFromCreatePerson)(
				clientApi.person,
			),
		}),
		decode: (client) => ({
			...client,
			person: Schema.decodeUnknownSync(CreatePersonApiFromCreatePerson)(
				client.person,
			),
		}),
	},
);

export const CreateClientApiResponse = Schema.String;

export const UpdateClientApi = Schema.Struct({
	id: Schema.String,
	person: UpdatePersonApi,
});

export const UpdateClientApiFromUpdateClient = Schema.transform(
	UpdateClient,
	UpdateClientApi,
	{
		strict: true,
		encode: (clientApi) => ({
			...clientApi,
			person: Schema.encodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
				clientApi.person,
			),
		}),
		decode: (client) => ({
			...client,
			person: Schema.decodeUnknownSync(UpdatePersonApiFromUpdatePerson)(
				client.person,
			),
		}),
	},
);

// Public Client Link API schemas
export const PublicClientLinkApi = Schema.Struct({
	id: Schema.String,
	schedule_id: Schema.String,
	worker_ids: Schema.mutable(Schema.Array(Schema.String)),
	client_id: Schema.String,
	url: Schema.String,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const PublicClientLinkFromApi = Schema.transform(
	PublicClientLinkApi,
	PublicClientLink,
	{
		strict: true,
		decode: (linkApi) => ({
			...linkApi,
			scheduleId: linkApi.schedule_id,
			workerIds: linkApi.worker_ids,
			clientId: linkApi.client_id,
			createdAt: linkApi.created_at,
			updatedAt: linkApi.updated_at,
			deletedAt: linkApi.deleted_at,
		}),
		encode: (link) => ({
			...link,
			schedule_id: link.scheduleId,
			worker_ids: link.workerIds,
			client_id: link.clientId,
			created_at: link.createdAt,
			updated_at: link.updatedAt,
			deleted_at: link.deletedAt,
		}),
	},
);

export const CreatePublicClientLinkApi = Schema.Struct({
	schedule_id: Schema.String,
	worker_ids: Schema.mutable(Schema.Array(Schema.String)),
	client_id: Schema.String,
	url: Schema.String,
});

export const CreatePublicClientLinkApiFromCreatePublicClientLink =
	Schema.transform(CreatePublicClientLink, CreatePublicClientLinkApi, {
		strict: true,
		decode: (linkApi) => ({
			...linkApi,
			schedule_id: linkApi.scheduleId,
			worker_ids: linkApi.workerIds,
			client_id: linkApi.clientId,
		}),
		encode: (link) => ({
			...link,
			scheduleId: link.schedule_id,
			workerIds: link.worker_ids,
			clientId: link.client_id,
		}),
	});

export const UpdatePublicClientLinkApi = Schema.Struct({
	id: Schema.String,
	schedule_id: Schema.String,
	worker_ids: Schema.mutable(Schema.Array(Schema.String)),
	client_id: Schema.String,
	url: Schema.String,
});

export const UpdatePublicClientLinkApiFromUpdatePublicClientLink =
	Schema.transform(UpdatePublicClientLink, UpdatePublicClientLinkApi, {
		strict: true,
		decode: (linkApi) => ({
			...linkApi,
			schedule_id: linkApi.scheduleId,
			worker_ids: linkApi.workerIds,
			client_id: linkApi.clientId,
		}),
		encode: (link) => ({
			...link,
			scheduleId: link.schedule_id,
			workerIds: link.worker_ids,
			clientId: link.client_id,
		}),
	});

// Session Info API schema
export const SessionInfoApi = Schema.Struct({
	id: Schema.String,
	client_id: Schema.NullOr(Schema.String),
	worker_id: Schema.String,
	turn_id: Schema.String,
	day: Schema.Number,
	time: Schema.Number,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const SessionInfoFromApi = Schema.transform(
	SessionInfoApi,
	SessionInfo,
	{
		strict: true,
		decode: (sessionApi) => ({
			...sessionApi,
			clientId: sessionApi.client_id,
			workerId: sessionApi.worker_id,
			turnId: sessionApi.turn_id,
			createdAt: sessionApi.created_at,
			updatedAt: sessionApi.updated_at,
			deletedAt: sessionApi.deleted_at,
		}),
		encode: (session) => ({
			...session,
			client_id: session.clientId,
			worker_id: session.workerId,
			turn_id: session.turnId,
			created_at: session.createdAt,
			updated_at: session.updatedAt,
			deleted_at: session.deletedAt,
		}),
	},
);

// Turn Info API schema
export const TurnInfoApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	start_time: Schema.Number,
	end_time: Schema.Number,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const TurnInfoFromApi = Schema.transform(TurnInfoApi, TurnInfo, {
	strict: true,
	decode: (turnApi) => ({
		...turnApi,
		startTime: turnApi.start_time,
		endTime: turnApi.end_time,
		createdAt: turnApi.created_at,
		updatedAt: turnApi.updated_at,
		deletedAt: turnApi.deleted_at,
	}),
	encode: (turn) => ({
		...turn,
		start_time: turn.startTime,
		end_time: turn.endTime,
		created_at: turn.createdAt,
		updated_at: turn.updatedAt,
		deleted_at: turn.deletedAt,
	}),
});

// Worker Info API schema
export const WorkerInfoApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	father_last_name: Schema.String,
	mother_last_name: Schema.String,
});

export const WorkerInfoFromApi = Schema.transform(WorkerInfoApi, WorkerInfo, {
	strict: true,
	decode: (workerApi) => ({
		...workerApi,
		fatherLastName: workerApi.father_last_name,
		motherLastName: workerApi.mother_last_name,
	}),
	encode: (worker) => ({
		...worker,
		father_last_name: worker.fatherLastName,
		mother_last_name: worker.motherLastName,
	}),
});

// Client Link Response API schema
export const ClientLinkResponseApi = Schema.Struct({
	sessions: Schema.mutable(Schema.Array(SessionInfoApi)),
	turns: Schema.mutable(Schema.Array(TurnInfoApi)),
	workers: Schema.mutable(Schema.Array(WorkerInfoApi)),
});

export const ClientLinkResponseFromApi = Schema.transform(
	ClientLinkResponseApi,
	ClientLinkResponse,
	{
		strict: true,
		decode: (responseApi) => ({
			sessions: responseApi.sessions.map((session) =>
				Schema.decodeUnknownSync(SessionInfoFromApi)(session),
			),
			turns: responseApi.turns.map((turn) =>
				Schema.decodeUnknownSync(TurnInfoFromApi)(turn),
			),
			workers: responseApi.workers.map((worker) =>
				Schema.decodeUnknownSync(WorkerInfoFromApi)(worker),
			),
		}),
		encode: (response) => ({
			sessions: response.sessions.map((session) =>
				Schema.encodeUnknownSync(SessionInfoFromApi)(session),
			),
			turns: response.turns.map((turn) =>
				Schema.encodeUnknownSync(TurnInfoFromApi)(turn),
			),
			workers: response.workers.map((worker) =>
				Schema.encodeUnknownSync(WorkerInfoFromApi)(worker),
			),
		}),
	},
);

export const GeneratePublicLinkResponse = Schema.String;
