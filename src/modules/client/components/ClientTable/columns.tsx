import { createColumnHelper } from "@tanstack/react-table";
import { Co<PERSON>, Edit, Link, Plus, Trash } from "lucide-react";
import { useState } from "react";
import { toast } from "react-toastify";
import { constants } from "~/config/constants";
import { DocumentType } from "~/person/service/model/person";
import type { Client } from "../../service/model/client";
import CreatePublicClientLinkModal from "../CreatePublicClientLinkModal/index";
import DeleteClientModal from "../DeleteClientModal";
import EditeClientModal from "../EditeClientModal";

const columnHelper = createColumnHelper<Client>();

export const columns = [
	columnHelper.accessor("person.name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("person.fatherLastName", {
		header: "Apellido Paterno",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("person.motherLastName", {
		header: "Apellido Materno",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("person.email", {
		header: "Email",
		cell: (info) => info.getValue() || "-",
	}),
	columnHelper.accessor("person.phone", {
		header: "Teléfono",
		cell: (info) => info.getValue() || "-",
	}),
	columnHelper.accessor("person.birthDate", {
		header: "Fecha de nacimiento",
		cell: (info) => info.getValue() || "-",
	}),
	columnHelper.accessor("person.document", {
		header: "Documento",
		cell: (info) => {
			const documentType = info.row.original.person.documentType;
			const document = info.getValue();
			const typeLabel =
				documentType === DocumentType.DNI
					? "DNI"
					: documentType === DocumentType.PASAPORTE
						? "Pasaporte"
						: "RUC";
			return `${typeLabel}: ${document}`;
		},
	}),
	columnHelper.accessor("publicLink", {
		header: "Link público",
		cell: ({ row, getValue }) => {
			const [isLinkOpen, setIsLinkOpen] = useState(false);
			const client = row.original;

			async function handleCopyLink() {
				await navigator.clipboard.writeText(
					`${constants.URL}/public-link/${getValue()}`,
				);
				toast.success("Enlace copiado al portapapeles");
			}

			return (
				<div className="flex gap-2">
					{getValue() ? (
						<button
							type="button"
							className="btn btn-sm btn-info"
							onClick={handleCopyLink}
						>
							<Copy size={16} />
						</button>
					) : (
						<>
							{/* TODO: Add button to update link */}
							<button type="button" className="btn btn-sm btn-primary" disabled>
								<Edit size={16} />
							</button>
							<button
								type="button"
								className="btn btn-sm btn-primary"
								onClick={() => setIsLinkOpen(true)}
							>
								<Plus size={16} />
							</button>
						</>
					)}

					<CreatePublicClientLinkModal
						isOpen={isLinkOpen}
						setIsOpen={setIsLinkOpen}
						client={client}
					/>
				</div>
			);
		},
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isEditOpen, setIsEditOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const client = row.original;

			return (
				<div className="flex gap-2">
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>

					<EditeClientModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={client.id}
					/>
					<DeleteClientModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						client={client}
					/>
				</div>
			);
		},
	}),
];
