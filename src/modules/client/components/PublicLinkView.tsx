import { useQuery } from "@tanstack/react-query";
import { AlertTriangle, Info, Lock } from "lucide-react";
import { useEffect, useState } from "react";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { clientLinkOptions } from "../hooks/client-options";
import type { TurnInfo, WorkerInfo } from "../service/model/publicClientLink";
import PublicSessionScheduleGrid from "./PublicSessionScheduleGrid";
import TurnSelector from "./TurnSelector";
import WorkerSelector from "./WorkerSelector";

interface PublicLinkViewProps {
	url: string; // The public link URL parameter
	sessionDuration?: number; // Default session duration in minutes
	breakDuration?: number; // Default break duration in minutes
}

export default function PublicLinkView({
	url,
	sessionDuration = 60,
	breakDuration = 15,
}: PublicLinkViewProps) {
	const svc = useService();
	const [selectedWorker, setSelectedWorker] = useState<WorkerInfo | null>(null);
	const [selectedTurn, setSelectedTurn] = useState<TurnInfo | null>(null);

	// Fetch client link data
	const {
		data: clientLinkData,
		isError,
		error,
		isPending,
	} = useQuery(clientLinkOptions(svc, url));

	useEffect(() => {
		if (isError) {
			console.log(getErrorResult(error).error);
		}
	}, [isError, error]);

	if (isPending) {
		return (
			<div className="flex items-center justify-center p-8">
				<div className="flex items-center gap-2">
					<span className="loading loading-spinner loading-md" />
					<span>Cargando información...</span>
				</div>
			</div>
		);
	}

	if (isError) {
		return (
			<div className="alert alert-error">
				<span>Error: {getErrorResult(error).error.message}</span>
			</div>
		);
	}

	if (!clientLinkData) {
		return (
			<div className="alert alert-warning">
				<span>No se encontró información para este enlace</span>
			</div>
		);
	}

	const { sessions, turns, workers } = clientLinkData;

	return (
		<div className="space-y-6">
			{/* Privacy Warning */}
			<div className="alert alert-warning border-warning bg-warning/10">
				<AlertTriangle className="h-5 w-5 shrink-0 stroke-current text-warning" />
				<div className="text-sm">
					<div className="font-semibold text-warning">
						⚠️ Aviso de Privacidad
					</div>
					<p className="mt-1">
						Este enlace es <strong>personal y confidencial</strong>. No lo
						compartas con otras personas para proteger tu privacidad y la
						disponibilidad de tus citas.
					</p>
				</div>
			</div>

			{/* Header */}
			<div className="text-center">
				<h1 className="font-bold text-2xl">Horarios Disponibles</h1>
				<p className="text-base-content/70">
					Selecciona un trabajador y turno para ver los horarios disponibles
				</p>
				<p className="mt-2 text-base-content/50 text-xs">
					Este enlace ha sido generado específicamente para ti
				</p>
			</div>

			{/* Controls */}
			<div className="card bg-base-100 shadow-sm">
				<div className="card-body">
					<div className="grid grid-cols-1 gap-4 md:grid-cols-2">
						{/* Worker Selector */}
						<div>
							<div className="label">
								<span className="label-text">Trabajador</span>
							</div>
							<WorkerSelector
								workers={workers}
								selectedWorker={selectedWorker}
								onWorkerChange={setSelectedWorker}
							/>
						</div>

						{/* Turn Selector */}
						<div>
							<div className="label">
								<span className="label-text">Turno</span>
							</div>
							<TurnSelector
								turns={turns}
								selectedTurn={selectedTurn}
								onTurnChange={setSelectedTurn}
							/>
						</div>
					</div>
				</div>
			</div>

			{/* Schedule Grid */}
			{selectedTurn && (
				<div className="card bg-base-100 shadow-sm">
					<div className="card-body">
						<h2 className="card-title">
							Horarios para {selectedTurn.name}
							{selectedWorker && (
								<span className="text-base-content/70">
									- {selectedWorker.name} {selectedWorker.fatherLastName}
								</span>
							)}
						</h2>
						<PublicSessionScheduleGrid
							sessions={sessions}
							turn={selectedTurn}
							selectedWorker={selectedWorker}
							sessionDuration={sessionDuration}
							breakDuration={breakDuration}
						/>
					</div>
				</div>
			)}

			{/* Legend */}
			<div className="card bg-base-100 shadow-sm">
				<div className="card-body">
					<h3 className="card-title text-sm">Leyenda</h3>
					<div className="flex flex-wrap gap-4 text-xs">
						<div className="flex items-center gap-2">
							<div className="h-4 w-4 rounded border border-base-content/25 bg-base-100" />
							<span>Disponible</span>
						</div>
						<div className="flex items-center gap-2">
							<div className="h-4 w-4 rounded bg-[#ff5c9b]" />
							<span>Ocupado</span>
						</div>
					</div>
				</div>
			</div>

			{/* Additional Privacy Notice */}
			<div className="card border border-base-300 bg-base-200/50">
				<div className="card-body py-3">
					<div className="flex items-start gap-3">
						<Info className="mt-0.5 h-4 w-4 shrink-0 text-info" />
						<div className="text-base-content/70 text-xs">
							<p className="mb-1 font-medium text-base-content/80">
								<Lock className="mr-1 inline h-3 w-3" />
								Información Importante:
							</p>
							<ul className="space-y-1">
								<li>• Este enlace es único y personal</li>
								<li>• Mantén la confidencialidad de esta URL</li>
								<li>
									• Si alguien más accede, podrían ver u ocupar tus horarios
									disponibles
								</li>
							</ul>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
