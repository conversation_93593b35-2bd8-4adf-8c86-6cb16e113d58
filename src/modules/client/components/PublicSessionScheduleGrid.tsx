import React from "react";
import type {
	SessionInfo,
	TurnInfo,
	WorkerInfo,
} from "../service/model/publicClientLink";

interface PublicSessionScheduleGridProps {
	sessions: SessionInfo[];
	turn: TurnInfo;
	selectedWorker: WorkerInfo | null;
	sessionDuration: number; // in minutes
	breakDuration: number; // in minutes
}

interface TimeSlot {
	start: number;
	end: number;
	label: string;
}

export default function PublicSessionScheduleGrid({
	sessions,
	turn,
	selectedWorker,
	sessionDuration,
	breakDuration,
}: PublicSessionScheduleGridProps) {
	const minutesToTimeString = (minutes: number) => {
		const hours = Math.floor(minutes / 60);
		const mins = minutes % 60;
		const period = hours >= 12 ? "PM" : "AM";
		const displayHours = hours > 12 ? hours - 12 : hours === 0 ? 12 : hours;
		return `${displayHours}:${mins.toString().padStart(2, "0")} ${period}`;
	};

	const generateTimeSlots = (): TimeSlot[] => {
		const slots: TimeSlot[] = [];

		// Convert start and end times to minutes
		const startTimeInMinutes =
			Math.floor(turn.startTime / 100) * 60 + (turn.startTime % 100);
		const endTimeInMinutes =
			Math.floor(turn.endTime / 100) * 60 + (turn.endTime % 100);

		let currentTime = startTimeInMinutes;

		while (currentTime < endTimeInMinutes) {
			const sessionEnd = currentTime + sessionDuration;

			// Check if the complete session fits within the time window
			if (sessionEnd > endTimeInMinutes) {
				break; // Not enough time for a complete session
			}

			slots.push({
				start: currentTime,
				end: sessionEnd,
				label: "Sesión",
			});

			// Move to next session time (including break duration for spacing)
			currentTime = sessionEnd + breakDuration;
		}

		return slots;
	};

	const timeSlots = generateTimeSlots();
	const days = [
		"Domingo",
		"Lunes",
		"Martes",
		"Miércoles",
		"Jueves",
		"Viernes",
		"Sábado",
	];

	// Filter sessions based on selected worker and turn
	const filteredSessions = sessions.filter((session) => {
		const matchesTurn = session.turnId === turn.id;
		const matchesWorker = selectedWorker
			? session.workerId === selectedWorker.id
			: true;
		return matchesTurn && matchesWorker;
	});

	const getSessionForSlot = (dayIndex: number, timeIndex: number) => {
		return filteredSessions.find(
			(session) => session.day === dayIndex && session.time === timeIndex,
		);
	};

	return (
		<div className="overflow-x-auto">
			<div
				className="grid gap-1 text-xs"
				style={{
					gridTemplateColumns: `120px repeat(${days.length}, 1fr)`,
					gridTemplateRows: `auto repeat(${timeSlots.length}, minmax(64px, auto))`,
				}}
			>
				{/* Header row */}
				<div className="flex items-center justify-center rounded bg-neutral p-2 font-medium text-white">
					Hora
				</div>
				{days.map((day) => (
					<div
						key={day}
						className="rounded bg-base-300 p-2 text-center font-medium"
					>
						{day}
					</div>
				))}

				{/* Time slots and content */}
				{timeSlots.map((slot, timeIndex) => (
					<React.Fragment key={`${slot.start}-${timeIndex}`}>
						<div className="flex items-center justify-center rounded bg-base-200 p-2 font-mono text-xs">
							{minutesToTimeString(slot.start)} -{" "}
							{minutesToTimeString(slot.end)}
						</div>
						{days.map((day, dayIndex) => {
							const existingSession = getSessionForSlot(dayIndex, timeIndex);
							const isOccupied = !!existingSession;

							return (
								<div key={day} className="flex items-center justify-center p-1">
									<div
										className={`flex h-16 w-full items-center justify-center rounded border-1 border-base-content/25 px-2 py-1 font-medium text-xs transition-colors ${
											isOccupied ? "bg-[#ff5c9b]" : ""
										}`}
									>
										{isOccupied ? (
											<span>Ocupado</span>
										) : (
											<span>Disponible</span>
										)}
									</div>
								</div>
							);
						})}
					</React.Fragment>
				))}
			</div>
		</div>
	);
}
